{"rustc": 8024708092284749966, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"rustc-dep-of-std\"]", "target": 14691992093392644261, "profile": 8829588955844408089, "path": 3645663646551335374, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\cfg-if-908a92df72917e80\\dep-lib-cfg_if", "checksum": false}}], "rustflags": ["-C", "target-cpu=native", "-C", "opt-level=z", "-C", "lto=fat", "-C", "codegen-units=1", "-C", "panic=abort", "-C", "strip=symbols"], "config": 2069994364910194474, "compile_kind": 0}