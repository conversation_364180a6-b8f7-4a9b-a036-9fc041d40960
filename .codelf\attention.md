## Development Guidelines

### Framework and Language
> 本项目使用Rust语言开发，专注于系统级编程和安全研究。

**Rust语言特性考虑:**
- 内存安全: 利用Rust的所有权系统避免内存泄漏和悬垂指针
- 零成本抽象: 使用Rust的trait系统实现高性能抽象
- 并发安全: 利用Rust的类型系统保证线程安全
- 系统调用: 直接使用unsafe代码块进行底层系统调用
- 重要注意事项:
	* 大量使用unsafe代码进行Windows API调用，需要特别注意内存安全
	* 使用winapi和windows-sys crate进行Windows API绑定
	* 需要处理C风格的指针和结构体

**Rust最佳实践:**
- 类型安全: 使用强类型系统防止运行时错误
- 错误处理: 使用Result<T, E>类型进行错误处理，避免panic
- 模式匹配: 充分利用match表达式处理枚举和Option类型
- 生命周期: 正确标注生命周期参数，避免借用检查器错误

### Code Abstraction and Reusability
> 开发过程中优先考虑代码抽象和可复用性，确保模块化和组件化功能。尽量寻找现有解决方案而不是重新发明轮子。

**模块化设计原则:**
- 单一职责: 每个模块只负责一个功能
- 高内聚低耦合: 相关功能集中，减少模块间依赖
- 稳定接口: 对外暴露稳定接口，内部实现可变

**可复用组件库:**
```
src/
- core/                          # 核心功能模块
    - crypto.rs                  # 加密解密通用函数
    - memory.rs                  # 内存管理通用接口
    - syscalls.rs                # 系统调用封装
- utils/                         # 工具函数库
    - winapi.rs                  # Windows API封装
    - random.rs                  # 随机数生成工具
    - pe_parser.rs               # PE文件解析工具
- defense/                       # 防御规避技术模块
    - evasion_manager.rs         # 统一规避管理接口
- config/                        # 配置管理模块
    - loader.rs                  # 配置加载通用接口
```

### Coding Standards and Tools
**Rust代码格式化工具:**
- rustfmt: Rust官方代码格式化工具
- clippy: Rust官方代码检查工具，提供lint建议
- cargo check: 快速类型检查
- cargo test: 运行单元测试和集成测试

**命名和结构约定:**
- 语义化命名: 变量/函数名应清楚表达其用途
- Rust命名风格: 使用snake_case命名变量和函数，PascalCase命名类型
- 模块结构遵循功能职责划分
- unsafe代码块必须添加详细注释说明安全性

### 系统级编程标准
**Windows API调用规范:**
- 安全的API调用: 所有Windows API调用都应在unsafe块中进行
- 错误处理: 检查API调用返回值，使用GetLastError获取详细错误信息
- 内存管理: 手动管理从Windows API分配的内存
- 句柄管理: 确保所有句柄都被正确关闭

**系统调用设计:**
- 间接调用: 使用动态系统调用号发现避免静态分析
- 参数验证: 验证所有传递给系统调用的参数
- 错误传播: 将系统调用错误正确传播到上层

### Performance and Security
**性能优化重点:**
- 内存分配优化
	* 使用高效的内存分配策略（Direct、SectionMapping、ModuleStomping）
	* 避免频繁的内存分配和释放
- 系统调用优化
	* 使用间接系统调用减少API Hook检测
	* 缓存系统调用号避免重复查找
- 加密性能优化
	* 使用硬件加速的AES实现
	* 实现JIT解密减少内存占用

**安全措施:**
- 反检测技术
	* 实现沙箱检测和规避
	* 使用AMSI和ETW绕过技术
	* 实现反调试和反分析技术
- 内存保护
	* 使用加密内存存储敏感数据
	* 实现内存清零防止数据泄露
- 身份伪装
	* 实现PPID欺骗和PEB伪装
	* 使用行为伪装技术模拟正常程序