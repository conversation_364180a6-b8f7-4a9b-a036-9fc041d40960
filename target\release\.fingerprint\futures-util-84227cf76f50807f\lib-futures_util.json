{"rustc": 8024708092284749966, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 1981027338247755584, "path": 5993240870952860799, "deps": [[5103565458935487, "futures_io", false, 666378575544274511], [1615478164327904835, "pin_utils", false, 12676885576622811967], [1906322745568073236, "pin_project_lite", false, 12762125192139160970], [3129130049864710036, "memchr", false, 14450648045447347776], [6955678925937229351, "slab", false, 1213581941517913688], [7013762810557009322, "futures_sink", false, 8740227797205956671], [7620660491849607393, "futures_core", false, 3569939882267476845], [16240732885093539806, "futures_task", false, 9723293631056925939]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-84227cf76f50807f\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}