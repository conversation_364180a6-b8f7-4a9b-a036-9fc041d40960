## Rust BypassAV - 高级反病毒绕过框架

> 这是一个用Rust编写的高级反病毒绕过框架，专门设计用于绕过现代反病毒软件和EDR系统的检测。

> 项目目的：提供一个功能完整、模块化的反病毒绕过解决方案，支持多种规避技术和执行策略。

> 项目状态：活跃开发中，当前版本支持AES加密、多种内存分配策略、系统调用强化、沙箱规避等功能。

> 开发团队：个人项目

> 技术栈：Rust语言，Windows API，系统调用，加密技术，反调试技术



## Dependencies (基于Cargo.toml)

* winapi (0.3): Windows API绑定，用于系统调用和Windows功能
* windows-sys (0.52): 现代Windows API绑定
* ntapi (0.4): Windows NT API绑定，用于底层系统操作
* aes (0.8): AES加密算法实现
* aes-gcm (0.10): AES-GCM认证加密
* cbc (0.1): CBC模式加密
* base64 (0.22): Base64编码/解码
* serde (1.0): 序列化/反序列化框架
* serde_json (1.0): JSON序列化支持
* rand (0.8): 随机数生成
* hex (0.4): 十六进制编码
* uuid (1.16): UUID生成
* chrono (0.4): 日期时间处理
* reqwest (0.12): HTTP客户端
* tokio (1.45): 异步运行时
* pelite (0.10): PE文件解析
* regex (1.11): 正则表达式
* log (0.4): 日志框架
* env_logger (0.10): 环境变量日志配置


## Development Environment

> 开发环境要求：
> - Rust 1.70+ (使用最新稳定版)
> - Windows 10/11 (目标平台)
> - PowerShell (用于构建脚本)
> - Visual Studio Build Tools (链接器)
>
> 构建脚本：
> - build.bat: 主构建脚本，支持debug和release模式
> - encrypt_shellcode.ps1: PowerShell脚本，用于加密shellcode
> - release.bat: 发布构建脚本


## Structrue (init from project tree)

> It is essential to consistently refine the analysis down to the file level — this level of granularity is of utmost importance.

> If the number of files is too large, you should at least list all the directories, and provide comments for the parts you consider particularly important.

> In the code block below, add comments to the directories/files to explain their functionality and usage scenarios.

> if you think the directory/file is not important, you can not skip it, just add a simple comment to it.

> but if you think the directory/file is important, you should read the files and add more detail comments on it (e.g. add comments on the functions, classes, and variables. explain the functionality and usage scenarios. write the importance of the directory/file).
```
root
- 360_BYPASS_GUIDE.md                    # 360安全软件绕过指南
- ANTI_DETECTION_OPTIMIZATION_REPORT.md # 反检测优化报告
- beacon.bin                             # 原始shellcode文件（Cobalt Strike beacon）
- build.bat                              # 主构建脚本，支持debug/release模式
- build.rs                               # Rust构建脚本，处理资源嵌入
- Cargo.lock                             # 依赖版本锁定文件
- Cargo.toml                             # Rust项目配置和依赖声明
- config.json                            # 主配置文件，包含所有规避技术配置
- encrypt_shellcode.ps1                  # PowerShell加密脚本，用于加密shellcode
- image/                                 # 文档图片目录
    - README/                            # README相关图片
        - *.png                          # 项目截图和说明图片
- kXRUequW.exe                          # 最新生成的release版本可执行文件
- PROJECT_SUMMARY.md                     # 项目总结文档
- README.md                              # 项目说明文档
- release.bat                            # 发布构建脚本
- shellcode_encrypted.txt                # 加密后的shellcode数据
- src/                                   # 主源码目录
    - config/                            # 配置模块，处理JSON配置解析
        - defense_config.rs              # 防御规避配置结构体定义
        - execution_config.rs            # 执行策略配置结构体定义
        - loader.rs                      # 配置加载器，包含所有配置结构体定义
        - memory_config.rs               # 内存分配配置结构体定义
        - mod.rs                         # 配置模块导出
        - sandbox_config.rs              # 沙箱规避配置结构体定义
    - constants.rs                       # 全局常量定义
    - core/                              # 核心功能模块
        - crypto.rs                      # 加密解密核心实现（AES、XOR等）
        - embedded.rs                    # 嵌入式资源管理
        - encrypted_memory.rs            # 加密内存管理
        - execution.rs                   # shellcode执行策略实现
        - memory.rs                      # 内存分配和管理核心功能
        - mod.rs                         # 核心模块导出
        - shellcode_loader.rs            # shellcode加载器核心实现
        - syscalls.rs                    # 系统调用管理和间接调用实现
    - defense/                           # 防御规避技术模块
        - amsi.rs                        # AMSI（反恶意软件扫描接口）绕过
        - anti_360.rs                    # 360安全软件专项绕过技术
        - behavior_camouflage.rs         # 行为伪装技术实现
        - cmdline.rs                     # 命令行欺骗技术
        - etw.rs                         # ETW（Windows事件跟踪）绕过
        - evasion_manager.rs             # 规避技术管理器，统一管理所有规避技术
        - identity.rs                    # 身份伪装技术（PEB、PPID等）
        - mod.rs                         # 防御模块导出
        - ppid.rs                        # 父进程ID欺骗技术
        - sandbox.rs                     # 沙箱检测和规避技术
        - startup_camouflage.rs          # 启动伪装技术
        - string_obfuscation.rs          # 字符串混淆技术
    - error/                             # 错误处理模块
        - mod.rs                         # 自定义错误类型定义
    - lib.rs                             # 库入口文件
    - main.rs                            # 主程序入口
    - orchestrator.rs                    # 主协调器，统一管理所有模块
    - utils/                             # 工具函数模块
        - code_cave_scanner.rs           # 代码洞扫描器，用于模块踩踏技术
        - logger.rs                      # 日志记录工具
        - mod.rs                         # 工具模块导出
        - pe_parser.rs                   # PE文件解析工具
        - random.rs                      # 随机数生成工具
        - winapi.rs                      # Windows API封装
        - winapi_utils.rs                # Windows API工具函数
- tests/                                 # 测试目录，包含各种单元测试和集成测试
    - advanced_memory_allocation_tests.rs    # 高级内存分配测试
    - basic_e2e_integration_tests.rs         # 基础端到端集成测试
    - cleanup_manager_tests.rs               # 清理管理器测试
    - config_loader_tests.rs                 # 配置加载器测试
    - config_validation_enhancement_tests.rs # 配置验证增强测试
    - crypto_aes256gcm_tests.rs              # AES-256-GCM加密测试
    - crypto_manager_tests.rs                # 加密管理器测试
    - crypto_xor_tests.rs                    # XOR加密测试
    - end_to_end_encryption_integration_tests.rs # 端到端加密集成测试
    - evasion_manager_integration_tests.rs   # 规避管理器集成测试
    - evasion_manager_tests.rs               # 规避管理器单元测试
    - execution_manager_*_tests.rs           # 各种执行管理器测试
    - memory_manager_tests.rs                # 内存管理器测试
    - module_stomping_integration_tests.rs   # 模块踩踏集成测试
    - orchestrator_*_tests.rs                # 协调器各种测试
    - shellcode_loader_tests.rs              # shellcode加载器测试
    - syscall_manager_tests.rs               # 系统调用管理器测试
    - test_config.json                       # 测试配置文件
    - test_shellcode_*.bin                   # 测试用shellcode文件
- tools/                                 # 开发工具目录
    - config_template_generator.rs       # 配置模板生成器
    - xor_encrypt_tool.rs                # XOR加密工具
- USAGE_INSTRUCTIONS.md                  # 使用说明文档
```
