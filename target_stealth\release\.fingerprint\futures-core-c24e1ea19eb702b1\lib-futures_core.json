{"rustc": 8024708092284749966, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cfg-target-has-atomic\", \"default\", \"portable-atomic\", \"std\", \"unstable\"]", "target": 9453135960607436725, "profile": 1981027338247755584, "path": 15270633309655483009, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-core-c24e1ea19eb702b1\\dep-lib-futures_core", "checksum": false}}], "rustflags": ["-C", "target-cpu=native", "-C", "opt-level=z", "-C", "lto=fat", "-C", "codegen-units=1", "-C", "panic=abort", "-C", "strip=symbols"], "config": 2069994364910194474, "compile_kind": 0}