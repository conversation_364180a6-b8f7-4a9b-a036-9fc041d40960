{"rustc": 8024708092284749966, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 8829588955844408089, "path": 9421104762069299659, "deps": [[555019317135488525, "regex_automata", false, 18069319106479432471], [2779309023524819297, "aho_corasick", false, 16650771743843046554], [3129130049864710036, "memchr", false, 14450648045447347776], [9408802513701742484, "regex_syntax", false, 15325999795617170781]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-b94515c98f4b2be8\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}