{"rustc": 8024708092284749966, "features": "[]", "declared_features": "[]", "target": 7529200858990304138, "profile": 2437635810466842010, "path": 9148377678230710874, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pin-project-lite-4f27db8e796e84fd\\dep-lib-pin_project_lite", "checksum": false}}], "rustflags": ["-C", "target-cpu=native", "-C", "opt-level=z", "-C", "lto=fat", "-C", "codegen-units=1", "-C", "panic=abort", "-C", "strip=symbols"], "config": 2069994364910194474, "compile_kind": 0}