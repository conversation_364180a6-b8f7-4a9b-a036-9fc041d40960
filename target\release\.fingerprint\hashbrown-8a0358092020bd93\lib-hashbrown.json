{"rustc": 8024708092284749966, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 8829588955844408089, "path": 9697045521662467671, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-8a0358092020bd93\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}