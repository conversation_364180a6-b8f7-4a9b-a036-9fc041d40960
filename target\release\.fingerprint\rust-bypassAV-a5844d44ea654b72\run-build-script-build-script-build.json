{"rustc": 8024708092284749966, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5704178739508310664, "build_script_build", false, 5879942120689506041]], "local": [{"RerunIfChanged": {"output": "release\\build\\rust-bypassAV-a5844d44ea654b72\\output", "paths": ["config.json", "shellcode_encrypted.txt", "beacon.bin"]}}], "rustflags": [], "config": 0, "compile_kind": 0}