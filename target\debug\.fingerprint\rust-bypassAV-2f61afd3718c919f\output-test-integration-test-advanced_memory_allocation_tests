{"$message_type":"diagnostic","message":"unused variable: `memory_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":10194,"byte_end":10208,"line_start":236,"line_end":236,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    let memory_manager = MemoryManager::new(&invalid_config, syscall_manager.clone());","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":10194,"byte_end":10208,"line_start":236,"line_end":236,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    let memory_manager = MemoryManager::new(&invalid_config, syscall_manager.clone());","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":"_memory_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `memory_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:236:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let memory_manager = MemoryManager::new(&invalid_config, syscall_manager.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_memory_manager`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `heap_memory_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":10595,"byte_end":10614,"line_start":244,"line_end":244,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let heap_memory_manager = MemoryManager::new(&heap_config, syscall_manager.clone());","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":10595,"byte_end":10614,"line_start":244,"line_end":244,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let heap_memory_manager = MemoryManager::new(&heap_config, syscall_manager.clone());","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":"_heap_memory_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `heap_memory_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:244:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m244\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let heap_memory_manager = MemoryManager::new(&heap_config, syscall_manager.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_heap_memory_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `direct_memory_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":10862,"byte_end":10883,"line_start":249,"line_end":249,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    let direct_memory_manager = MemoryManager::new(&direct_config, syscall_manager);","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":10862,"byte_end":10883,"line_start":249,"line_end":249,"column_start":9,"column_end":30,"is_primary":true,"text":[{"text":"    let direct_memory_manager = MemoryManager::new(&direct_config, syscall_manager);","highlight_start":9,"highlight_end":30}],"label":null,"suggested_replacement":"_direct_memory_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `direct_memory_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:249:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let direct_memory_manager = MemoryManager::new(&direct_config, syscall_manager);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_direct_memory_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `memory_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":12306,"byte_end":12320,"line_start":288,"line_end":288,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let memory_manager = MemoryManager::new(&config, syscall_manager.clone());","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":12306,"byte_end":12320,"line_start":288,"line_end":288,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let memory_manager = MemoryManager::new(&config, syscall_manager.clone());","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"_memory_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `memory_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:288:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let memory_manager = MemoryManager::new(&config, syscall_manager.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_memory_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `memory_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":16539,"byte_end":16553,"line_start":389,"line_end":389,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"            let memory_manager = MemoryManager::new(&config, syscall_manager);","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":16539,"byte_end":16553,"line_start":389,"line_end":389,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"            let memory_manager = MemoryManager::new(&config, syscall_manager);","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":"_memory_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `memory_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:389:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m389\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let memory_manager = MemoryManager::new(&config, syscall_manager);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_memory_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `codecave_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":17276,"byte_end":17292,"line_start":409,"line_end":409,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let codecave_manager = MemoryManager::new(&codecave_config, syscall_manager.clone());","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":17276,"byte_end":17292,"line_start":409,"line_end":409,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let codecave_manager = MemoryManager::new(&codecave_config, syscall_manager.clone());","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"_codecave_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `codecave_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:409:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m409\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let codecave_manager = MemoryManager::new(&codecave_config, syscall_manager.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_codecave_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `functionhook_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":17902,"byte_end":17922,"line_start":420,"line_end":420,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    let functionhook_manager = MemoryManager::new(&functionhook_config, syscall_manager);","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"tests\\advanced_memory_allocation_tests.rs","byte_start":17902,"byte_end":17922,"line_start":420,"line_end":420,"column_start":9,"column_end":29,"is_primary":true,"text":[{"text":"    let functionhook_manager = MemoryManager::new(&functionhook_config, syscall_manager);","highlight_start":9,"highlight_end":29}],"label":null,"suggested_replacement":"_functionhook_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `functionhook_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\advanced_memory_allocation_tests.rs:420:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m420\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let functionhook_manager = MemoryManager::new(&functionhook_config, syscall_manager);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_functionhook_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 7 warnings emitted\u001b[0m\n\n"}
