{"rustc": 8024708092284749966, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 8829588955844408089, "path": 11627149989591878935, "deps": [[10411997081178400487, "cfg_if", false, 12120391511444849545]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\encoding_rs-2573f1fd1ba32478\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}