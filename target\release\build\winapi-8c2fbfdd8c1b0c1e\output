cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="limits"
cargo:rustc-cfg=feature="wtypes"
cargo:rustc-cfg=feature="unknwnbase"
cargo:rustc-cfg=feature="objidlbase"
cargo:rustc-cfg=feature="devpropdef"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="propidl"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-cfg=feature="rpc"
cargo:rustc-cfg=feature="oaidl"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="winreg"
cargo:rustc-cfg=feature="vadefs"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="shtypes"
cargo:rustc-cfg=feature="rpcdce"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="combaseapi"
cargo:rustc-cfg=feature="wincred"
cargo:rustc-cfg=feature="sspi"
cargo:rustc-cfg=feature="subauth"
cargo:rustc-cfg=feature="wtypesbase"
cargo:rustc-cfg=feature="wingdi"
cargo:rustc-cfg=feature="evntprov"
cargo:rustc-cfg=feature="lsalookup"
cargo:rustc-cfg=feature="wincontypes"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="reason"
cargo:rustc-cfg=feature="rpcndr"
cargo:rustc-cfg=feature="timezoneapi"
cargo:rustc-cfg=feature="evntcons"
cargo:rustc-cfg=feature="objidl"
cargo:rustc-cfg=feature="cfgmgr32"
cargo:rustc-cfg=feature="wmistr"
cargo:rustc-link-lib=dylib=winapi_advapi32
cargo:rustc-link-lib=dylib=winapi_cfgmgr32
cargo:rustc-link-lib=dylib=winapi_credui
cargo:rustc-link-lib=dylib=winapi_gdi32
cargo:rustc-link-lib=dylib=winapi_kernel32
cargo:rustc-link-lib=dylib=winapi_msimg32
cargo:rustc-link-lib=dylib=winapi_ole32
cargo:rustc-link-lib=dylib=winapi_opengl32
cargo:rustc-link-lib=dylib=winapi_secur32
cargo:rustc-link-lib=dylib=winapi_shell32
cargo:rustc-link-lib=dylib=winapi_synchronization
cargo:rustc-link-lib=dylib=winapi_user32
cargo:rustc-link-lib=dylib=winapi_winspool
