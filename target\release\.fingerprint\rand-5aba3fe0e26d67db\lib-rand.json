{"rustc": 8024708092284749966, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 8829588955844408089, "path": 1604395271644707724, "deps": [[1573238666360410412, "rand_chacha", false, 11769356667548424324], [18130209639506977569, "rand_core", false, 2590161656187875720]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-5aba3fe0e26d67db\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}