#include <windows.h>

// 版本信息
VS_VERSION_INFO VERSIONINFO
FILEVERSION     1,2,3,4
PRODUCTVERSION  1,2,3,4
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEFLAGS       0
FILEOS          VOS__WINDOWS32
FILETYPE        VFT_APP
FILESUBTYPE     VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904E4"
        BEGIN
            VALUE "CompanyName",        "Microsoft Corporation"
            VALUE "FileDescription",   "Windows System Health Monitor"
            VALUE "FileVersion",       "*******"
            VALUE "InternalName",      "SystemHealthMonitor"
            VALUE "LegalCopyright",    "Copyright (C) Microsoft Corporation. All rights reserved."
            VALUE "OriginalFilename",  "SystemHealthMonitor.exe"
            VALUE "ProductName",       "Microsoft Windows Operating System"
            VALUE "ProductVersion",    "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1252
    END
END

// 图标 (使用Windows默认图标)
1 ICON "C:\\Windows\\System32\\shell32.dll,1"

// 清单文件
1 RT_MANIFEST "manifest.xml"
