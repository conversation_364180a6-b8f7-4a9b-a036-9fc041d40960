#pragma code_page(65001)
1 VERSIONINFO
FILEFLAGS 0x0
PRODUCTVERSION 0, 1, 0, 0
FILEOS 0x40004
FILEVERSION 0, 1, 0, 0
FILETYPE 0x1
FILESUBTYPE 0x0
FILEFLAGSMASK 0x3f
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "FileVersion", "*******"
VALUE "OriginalFilename", "SystemHealthChecker.exe"
VALUE "InternalName", "SystemHealthChecker.exe"
VALUE "ProductVersion", "*******"
VALUE "LegalCopyright", "© Microsoft Corporation. All rights reserved."
VALUE "ProductName", "Windows System Health Monitor"
VALUE "FileDescription", "System Health Monitor Service"
VALUE "CompanyName", "Microsoft Corporation"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}

