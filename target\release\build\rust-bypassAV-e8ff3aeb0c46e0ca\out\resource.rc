#pragma code_page(65001)
1 VERSIONINFO
FILEOS 0x40004
PRODUCTVERSION 0, 1, 0, 0
FILETYPE 0x1
FILEFLAGSMASK 0x3f
FILEFLAGS 0x0
FILESUBTYPE 0x0
FILEVERSION 0, 1, 0, 0
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "InternalName", "SystemHealthChecker.exe"
VALUE "FileDescription", "System Health Monitor Service"
VALUE "FileVersion", "*******"
VALUE "ProductVersion", "*******"
VALUE "OriginalFilename", "SystemHealthChecker.exe"
VALUE "LegalCopyright", "© Microsoft Corporation. All rights reserved."
VALUE "CompanyName", "Microsoft Corporation"
VALUE "ProductName", "Windows System Health Monitor"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}

