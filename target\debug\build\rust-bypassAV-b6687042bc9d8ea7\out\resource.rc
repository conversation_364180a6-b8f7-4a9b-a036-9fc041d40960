#pragma code_page(65001)
1 VERSIONINFO
FILEFLAGS 0x0
FILEOS 0x40004
FILEVERSION 0, 1, 0, 0
FILETYPE 0x1
FILESUBTYPE 0x0
FILEFLAGSMASK 0x3f
PRODUCTVERSION 0, 1, 0, 0
{
BL<PERSON><PERSON> "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "CompanyName", "Microsoft Corporation"
VALUE "OriginalFilename", "SystemHealthChecker.exe"
VALUE "ProductName", "Windows System Health Monitor"
VALUE "ProductVersion", "*******"
VALUE "FileVersion", "*******"
VALUE "LegalCopyright", "© Microsoft Corporation. All rights reserved."
VALUE "InternalName", "SystemHealthChecker.exe"
VALUE "FileDescription", "System Health Monitor Service"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}

