#pragma code_page(65001)
1 VERSIONINFO
FILETYPE 0x1
FILEVERSION 0, 1, 0, 0
PRODUCTVERSION 0, 1, 0, 0
FILEOS 0x40004
FILESUBTYPE 0x0
FILEFLAGSMASK 0x3f
FILEFLAGS 0x0
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "ProductVersion", "*******"
VALUE "InternalName", "SystemHealthChecker.exe"
VALUE "LegalCopyright", "© Microsoft Corporation. All rights reserved."
VALUE "OriginalFilename", "SystemHealthChecker.exe"
VALUE "FileVersion", "*******"
VALUE "ProductName", "Windows System Health Monitor"
VALUE "CompanyName", "Microsoft Corporation"
VALUE "FileDescription", "System Utility (Debug)"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}

