{"rustc": 8024708092284749966, "features": "[\"alloc\", \"default\", \"derive\", \"zeroize_derive\"]", "declared_features": "[\"aarch64\", \"alloc\", \"default\", \"derive\", \"serde\", \"simd\", \"std\", \"zeroize_derive\"]", "target": 12572013220049634676, "profile": 8829588955844408089, "path": 5440472134502820274, "deps": [[15553062592622223563, "zeroize_derive", false, 5098957941732752337]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zeroize-7bf66622a8caa4b5\\dep-lib-zeroize", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}