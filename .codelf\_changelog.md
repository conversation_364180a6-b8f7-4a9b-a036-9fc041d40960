## 2025-06-06 09:30:00

### 1. 修复配置文件结构体不匹配问题

**Change Type**: fix

> **Purpose**: 解决SystemHealthChecker_v3.0_AntiDetection.exe无法正常运行的问题
> **Detailed Description**: 修复了配置文件中缺少的字段和结构体定义不匹配的问题，包括sectionNamePrefix、apiNameObfuscation等字段
> **Reason for Change**: 原有配置文件与代码中的结构体定义不匹配，导致程序启动时配置解析失败
> **Impact Scope**: 影响配置加载器、规避管理器、行为伪装模块
> **API Changes**: 更新了BehaviorCamouflageConfig和StringObfuscationConfig结构体定义
> **Configuration Changes**: 在config.json中添加了缺少的字段：sectionNamePrefix、apiNameObfuscation
> **Performance Impact**: 无性能影响，仅修复功能性问题

   ```
   root
   - config.json                        // fix - 添加缺少的配置字段
   - src/config/loader.rs               // fix - 更新结构体定义
   - src/defense/evasion_manager.rs     // fix - 修复字段访问错误
   - tools/config_template_generator.rs // fix - 更新模板生成器
   ```

### 2. 修复shellcode解密密钥不匹配问题

**Change Type**: fix

> **Purpose**: 解决AES-128-CBC解密失败的问题
> **Detailed Description**: 更新config.json中的加密密钥和IV，使其与shellcode_encrypted.txt文件中的密钥匹配
> **Reason for Change**: 配置文件中的密钥与实际加密使用的密钥不一致，导致shellcode解密失败
> **Impact Scope**: 影响shellcode加载器和加密模块
> **API Changes**: 无API变更
> **Configuration Changes**: 更新config.json中的key和iv字段
> **Performance Impact**: 无性能影响，修复解密功能

   ```
   root
   - config.json                        // fix - 更新加密密钥和IV
   ```

### 3. 优化内存分配策略配置

**Change Type**: improvement

> **Purpose**: 提供更灵活的内存分配策略选择
> **Detailed Description**: 支持在Direct和SectionMapping之间切换，解决NtCreateSection失败的问题
> **Reason for Change**: 某些环境下SectionMapping可能失败，需要fallback到Direct分配
> **Impact Scope**: 影响内存管理模块和执行策略
> **API Changes**: 无API变更
> **Configuration Changes**: 支持动态切换allocationStrategy配置
> **Performance Impact**: Direct分配性能更好，但隐蔽性稍差

   ```
   root
   - config.json                        // improvement - 优化内存分配配置
   ```

### 4. 生成新的release版本可执行文件

**Change Type**: build

> **Purpose**: 生成修复所有问题后的正式release版本
> **Detailed Description**: 构建了包含所有修复和优化的release版本，文件名为kXRUequW.exe
> **Reason for Change**: 提供稳定可用的release版本供实际使用
> **Impact Scope**: 整个项目的最终输出
> **API Changes**: 无API变更
> **Configuration Changes**: 恢复所有高级功能配置（沙箱规避、系统调用强化等）
> **Performance Impact**: release版本性能优化，去除debug信息

   ```
   root
   - kXRUequW.exe                       // add - 新生成的release版本
   - build.bat                          // - 构建脚本正常工作
   ```